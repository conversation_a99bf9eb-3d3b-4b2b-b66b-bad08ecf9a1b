describe('All Links Verification', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000/')
  })
    const acceptedStatusCodes = [200, 301, 302, 403, 404, 500]

  // describe('Service Accordion', () => {
  //   it("checks service accordion's link", () => {
  //     cy.get('.grid-in-service-accordion').contains('service page').click()
  //     cy.url().should('include', '/services')
  //     cy.go('back')
  //   })
  // })

  // describe('Ascension Logs', () => {
  //   it('checks ascension logs links', () => {
  //     cy.get('.grid-in-ascension-logs').contains('Recruiterly').click()
  //     cy.url().should('include', '/projects/recruiterly')
  //     cy.go('back')

  //     cy.get('.grid-in-ascension-logs').contains('Diobox').click()
  //     cy.url().should('include', '/projects/diobox')
  //     cy.go('back')
  //   })
  // })

  // describe('Service Carousel Cards', () => {
  //   it('checks all service carousel card links', () => {
  //     cy.get('.grid-in-carousel')
  //       .children()
  //       .first()
  //       .children()
  //       .first()
  //       .find('a')
  //       .each($link => {
  //         const href = $link.attr('href')
  //         cy.wrap($link).click()
  //         cy.url().should('include', href)
  //         cy.go('back')
  //       })
  //   })
  // })

  // describe('Client Feedback Carousel Cards', () => {
  //   it('checks all client feedback external links', () => {
  //     cy.get('.grid-in-carousel')
  //       .children()
  //       .first()
  //       .children()
  //       .last()
  //       .find('a')
  //       .each($link => {
  //         const url = $link.attr('href')
  //         if (!url || !isExternalLink(url)) return
  //         cy.request(url).then(response => {
  //           expect(response.status).to.be.oneOf(acceptedStatusCodes)
  //         })
  //       })

  //      it('verifies client feedback "See more" link works', () => {
  //        cy.get('.grid-in-carousel').contains('See more!').click()
  //        cy.url().should('include', '/client-feedback')
  //        cy.go('back')
  //      })
  //   })
  // })

  // describe('Project Carousel Cards', () => {
  //   it('checks all project carousel card links', () => {
  //     cy.get('.grid-in-carousel')
  //       .children()
  //       .last()
  //       .find('a')
  //       .each($link => {
  //         const href = $link.attr('href')
  //         cy.wrap($link).click()
  //         cy.url().should('include', href)
  //         cy.go('back')
  //       })
  //   })
  // })

  describe('Footer Links', () => {
    it('verifies footer internal links', () => {
      cy.get('footer').contains('manystack').click()
      cy.url().should('include', '/')

      cy.get('footer').contains('Our Projects').click()
      cy.url().should('include', '/projects')
      cy.go('back')

      cy.get('footer').contains('Our Services').click()
      cy.url().should('include', '/services')
      cy.go('back')

      cy.get('footer').contains('Privacy Promise').click()
      cy.url().should('include', '/policies/privacy-promise')
      cy.go('back')

      cy.get('footer').contains('Friendly Terms').click()
      cy.url().should('include', '/policies/friendly-terms')
      cy.go('back')
    })

    it('verifies footer social media links', () => {
      cy.request({
        url: 'https://facebook.com/mnystck',
        failOnStatusCode: false, // We don't have a facebook page, and that gives back 400
      }).then(response => {
        expect(response.status).to.be.oneOf([...acceptedStatusCodes, 400])
      })
      cy.request({ url: 'https://x.com/mnystck' }).then(response => {
        expect(response.status).to.be.oneOf(acceptedStatusCodes)
      })
      cy.request({ url: 'https://www.linkedin.com/company/manystack' }).then(response => {
        expect(response.status).to.be.oneOf(acceptedStatusCodes)
      })
    })

    it('verifies footer email link functionality', () => {
      cy.get('footer').contains('Drop an email').should('exist')

      // Wait for email link to be built
      cy.wait(1000)

      cy.get('footer')
        .contains('Drop an email')
        .should('have.attr', 'href')
        .and('include', 'mailto:')
        .and('include', '<EMAIL>')
        .and('include', 'subject=My%20dream')
    })
  })

  describe('External Links', () => {
    it('verifies all external links have proper attributes', () => {
      cy.get('a[href^="http"]').each($link => {
        const href = $link.attr('href')
        if (href && !href.includes('localhost')) {
          cy.wrap($link)
            .should('have.attr', 'target', '_blank')
            .should('have.attr', 'href')
            .and('match', /^https?:\/\//)
        }
      })
    })
  })
})
